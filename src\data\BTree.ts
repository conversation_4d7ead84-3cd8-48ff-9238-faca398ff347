export interface BTreeNode {
  keys: number[];
  children: BTreeNode[];
  isLeaf: boolean;
  x?: number;
  y?: number;
  id: string;
}

export interface BTreeOperation {
  type: 'insert' | 'split' | 'search';
  node?: BTreeNode;
  key?: number;
  description: string;
}

export class BTree {
  root: BTreeNode;
  t: number; // Minimum degree (defines the range for number of keys)
  operations: BTreeOperation[] = [];

  constructor(t: number = 3) {
    this.t = t;
    this.root = this.createNode(true);

    // Initialize with a default key to ensure the root is never empty
    this.root.keys = [50];

    // Initialize operations array
    this.operations = [];

    // Add an initial operation for the default key
    this.operations.push({
      type: 'insert',
      key: 50,
      description: 'Initial key added to root'
    });
  }

  // Create a new node
  createNode(isLeaf: boolean): BTreeNode {
    return {
      keys: [],
      children: [],
      isLeaf,
      id: `node-${Date.now()}-${Math.random().toString(36).slice(2, 11)}`
    };
  }

  // Insert a key
  insert(key: number): void {
    // Add a separator operation to indicate a new key insertion
    if (this.operations.length > 0) {
      this.operations.push({
        type: 'insert',
        key,
        description: `Starting insertion of key ${key}`
      });
    }

    // If root is full, split it
    if (this.root.keys.length === 2 * this.t - 1) {
      const newRoot = this.createNode(false);
      newRoot.children.push(this.root);

      this.operations.push({
        type: 'split',
        node: this.root,
        description: `Root node is full, creating new root and splitting`
      });

      this.splitChild(newRoot, 0);
      this.root = newRoot;
      this.insertNonFull(newRoot, key);
    } else {
      this.insertNonFull(this.root, key);
    }
  }

  // Insert a key in a non-full node
  private insertNonFull(node: BTreeNode, key: number): void {
    let i = node.keys.length - 1;

    this.operations.push({
      type: 'search',
      node,
      key,
      description: `Searching for position to insert key ${key} in node with keys [${node.keys.join(', ')}]`
    });

    // If this is a leaf node
    if (node.isLeaf) {
      // Find position to insert
      while (i >= 0 && key < node.keys[i]) {
        node.keys[i + 1] = node.keys[i];
        i--;
      }

      // Insert the key
      node.keys[i + 1] = key;

      this.operations.push({
        type: 'insert',
        node,
        key,
        description: `Inserted key ${key} into leaf node, new keys: [${node.keys.join(', ')}]`
      });
    } else {
      // Find the child which is going to have the new key
      while (i >= 0 && key < node.keys[i]) {
        i--;
      }
      i++;

      // If the child is full, split it
      if (node.children[i].keys.length === 2 * this.t - 1) {
        this.operations.push({
          type: 'split',
          node: node.children[i],
          description: `Child node is full, splitting before insertion`
        });

        this.splitChild(node, i);

        // After split, the middle key goes up and the node splits into two
        if (key > node.keys[i]) {
          i++;
        }
      }

      this.insertNonFull(node.children[i], key);
    }
  }

  // Split a child of a node
  private splitChild(parent: BTreeNode, index: number): void {
    const child = parent.children[index];
    const newChild = this.createNode(child.isLeaf);

    // Move the second half of keys from child to newChild
    for (let j = 0; j < this.t - 1; j++) {
      newChild.keys[j] = child.keys[j + this.t];
    }

    // If not leaf, move the second half of children too
    if (!child.isLeaf) {
      for (let j = 0; j < this.t; j++) {
        newChild.children[j] = child.children[j + this.t];
      }
    }

    // Reduce the number of keys in child
    child.keys.length = this.t - 1;

    // Since parent is going to have a new child, create space for it
    for (let j = parent.children.length; j > index + 1; j--) {
      parent.children[j] = parent.children[j - 1];
    }

    // Link the new child to parent
    parent.children[index + 1] = newChild;

    // A key from child will move to parent
    // Find the location of the new key and move all greater keys one space ahead
    for (let j = parent.keys.length - 1; j >= index; j--) {
      parent.keys[j + 1] = parent.keys[j];
    }

    // Copy the middle key of child to parent
    parent.keys[index] = child.keys[this.t - 1];

    this.operations.push({
      type: 'split',
      node: parent,
      description: `Split complete: middle key ${child.keys[this.t - 1]} moved to parent`
    });
  }

  // Calculate the coordinates for visualization
  calculateCoordinates(): void {
    const verticalSpacing = 70; // Increased vertical spacing to prevent overlap

    // First pass: assign initial y-coordinates based on depth
    this.assignYCoordinates(this.root, verticalSpacing);

    // Second pass: assign x-coordinates using a more compact algorithm
    this.assignXCoordinates(this.root, 400); // Start at x=400 (center)

    // Third pass: center each level
    this.centerEachLevel();
  }

  // First pass: assign y-coordinates based on depth
  private assignYCoordinates(node: BTreeNode, verticalSpacing: number, depth: number = 0): void {
    if (!node) return;

    // Set y-coordinate based on depth
    node.y = depth * verticalSpacing + 40;

    // Process children
    if (!node.isLeaf) {
      for (const child of node.children) {
        this.assignYCoordinates(child, verticalSpacing, depth + 1);
      }
    }
  }

  // Second pass: assign x-coordinates using a more compact algorithm
  private assignXCoordinates(node: BTreeNode, startX: number): number {
    if (!node) return startX;

    if (node.isLeaf) {
      // For leaf nodes, just place them at the current position
      node.x = startX;
      // Calculate width based on number of keys
      const nodeWidth = Math.max(node.keys.length * 20, 30);
      return startX + nodeWidth + 10; // Add node width plus a small gap
    }

    // For internal nodes with children
    if (node.children.length === 0) {
      node.x = startX;
      const nodeWidth = Math.max(node.keys.length * 20, 30);
      return startX + nodeWidth + 10;
    }

    // Process first child
    let currentX = this.assignXCoordinates(node.children[0], startX);

    // Process remaining children with adequate spacing to prevent overlap
    for (let i = 1; i < node.children.length; i++) {
      // Add more space between children based on their key count
      const prevChildKeys = node.children[i-1].keys.length;
      const minGap = Math.max(30, prevChildKeys * 15); // Minimum gap based on previous node's keys
      currentX = this.assignXCoordinates(node.children[i], currentX + minGap);
    }

    // Position this node centered over its children
    const firstChildX = node.children[0].x || startX;
    const lastChildX = node.children[node.children.length - 1].x || startX;
    node.x = (firstChildX + lastChildX) / 2;

    return currentX;
  }

  // Third pass: center each level horizontally
  private centerEachLevel(): void {
    // Group nodes by level (y-coordinate)
    const nodesByLevel: Map<number, BTreeNode[]> = new Map();

    // Collect all nodes
    const allNodes = this.getAllNodes();

    // Group by y-coordinate
    allNodes.forEach(node => {
      const y = node.y || 0;
      if (!nodesByLevel.has(y)) {
        nodesByLevel.set(y, []);
      }
      nodesByLevel.get(y)?.push(node);
    });

    // Center each level
    nodesByLevel.forEach(nodes => {
      // Find min and max x on this level
      const minX = Math.min(...nodes.map(n => n.x || 0));
      const maxX = Math.max(...nodes.map(n => n.x || 0));

      // Calculate center of this level
      const center = (minX + maxX) / 2;

      // Shift all nodes to center around 400
      const offset = 400 - center;

      // Apply offset to all nodes on this level
      nodes.forEach(node => {
        if (node.x !== undefined) {
          node.x += offset;
        }
      });
    });
  }

  // Helper method to get the maximum depth of the tree
  private getMaxDepth(node: BTreeNode): number {
    if (node.isLeaf) return 1;

    let maxChildDepth = 0;
    for (const child of node.children) {
      maxChildDepth = Math.max(maxChildDepth, this.getMaxDepth(child));
    }

    return maxChildDepth + 1;
  }

  // Get all nodes for visualization
  getAllNodes(): BTreeNode[] {
    const nodes: BTreeNode[] = [];

    const traverse = (node: BTreeNode) => {
      nodes.push(node);
      if (!node.isLeaf) {
        for (const child of node.children) {
          traverse(child);
        }
      }
    };

    traverse(this.root);
    return nodes;
  }

  // Get all edges for visualization
  getEdges(): { source: BTreeNode; target: BTreeNode }[] {
    const edges: { source: BTreeNode; target: BTreeNode }[] = [];

    const traverse = (node: BTreeNode) => {
      if (!node.isLeaf) {
        for (const child of node.children) {
          edges.push({ source: node, target: child });
          traverse(child);
        }
      }
    };

    traverse(this.root);
    return edges;
  }
}
