import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { AVLTree, AVLNode } from '../data/AVLTree';
import { RedBlackTree, RBNode, Color } from '../data/RedBlackTree';
import { BTree, BTreeNode } from '../data/BTree';

type TreeType = 'avl' | 'rb' | 'btree';

interface TreeVisualizerProps {
  treeType: TreeType;
  values: number[];
  animationSpeed: number;
  isPlaying: boolean;
  onStepChange: (currentStep: number, totalSteps: number) => void;
}

const TreeVisualizer: React.FC<TreeVisualizerProps> = ({
  treeType,
  values,
  animationSpeed,
  isPlaying,
  onStepChange,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [totalSteps, setTotalSteps] = useState(0);
  const [avlTree] = useState(new AVLTree());
  const [rbTree] = useState(new RedBlackTree());
  const [bTree] = useState(new BTree(3)); // B-Tree of order 3
  const [operationDescription, setOperationDescription] = useState('');
  const [animationTimeout, setAnimationTimeout] = useState<NodeJS.Timeout | null>(null);

  // Reset and build tree when values change
  useEffect(() => {
    resetTree();
    if (values.length > 0) {
      buildTree();
    }
  }, [values, treeType]);

  // Handle animation when isPlaying changes
  useEffect(() => {
    if (isPlaying) {
      startAnimation();
    } else {
      stopAnimation();
    }
  }, [isPlaying, animationSpeed, currentStep]);

  // Update step info when current step changes
  useEffect(() => {
    onStepChange(currentStep, totalSteps);
    updateOperationDescription();
  }, [currentStep, totalSteps]);

  // Reset the tree
  const resetTree = () => {
    if (animationTimeout) {
      clearTimeout(animationTimeout);
      setAnimationTimeout(null);
    }
    setCurrentStep(0);
    setTotalSteps(0);
    setOperationDescription('');

    // Clear the SVG
    if (svgRef.current) {
      d3.select(svgRef.current).selectAll('*').remove();
    }
  };

  // Build the tree step by step
  const buildTree = () => {
    switch (treeType) {
      case 'avl':
        buildAVLTree();
        break;
      case 'rb':
        buildRBTree();
        break;
      case 'btree':
        buildBTree();
        break;
    }
  };

  // Build AVL Tree
  const buildAVLTree = () => {
    avlTree.root = null;
    avlTree.operations = [];

    // Insert values one by one
    values.forEach(value => {
      avlTree.insert(value);
    });

    // Calculate coordinates for visualization with better spacing
    const maxDepth = getTreeDepth(avlTree.root);
    const nodeCount = avlTree.getAllNodes().length;

    // Use appropriate spacing based on tree size
    const horizontalSpacing = Math.min(60, 400 / Math.max(1, Math.pow(2, Math.min(maxDepth, 4))));
    const verticalSpacing = Math.min(60, 300 / Math.max(1, maxDepth));
    avlTree.calculateCoordinates(avlTree.root, 0, 0, horizontalSpacing, verticalSpacing);

    // Set total steps
    setTotalSteps(avlTree.operations.length);

    // Draw initial tree
    drawAVLTree(0);
  };

  // Helper function to get tree depth
  const getTreeDepth = (node: any): number => {
    if (!node) return 0;
    if (node === (rbTree.NIL || null)) return 0;

    const leftDepth = node.left ? getTreeDepth(node.left) : 0;
    const rightDepth = node.right ? getTreeDepth(node.right) : 0;

    return Math.max(leftDepth, rightDepth) + 1;
  };

  // Build Red-Black Tree
  const buildRBTree = () => {
    rbTree.root = rbTree.NIL;
    rbTree.operations = [];

    // Insert values one by one
    values.forEach(value => {
      rbTree.insert(value);
    });

    // Calculate coordinates for visualization with better spacing
    const maxDepth = getTreeDepth(rbTree.root);
    const nodeCount = rbTree.getAllNodes().length;

    // Use appropriate spacing based on tree size
    const horizontalSpacing = Math.min(60, 400 / Math.max(1, Math.pow(2, Math.min(maxDepth, 4))));
    const verticalSpacing = Math.min(60, 300 / Math.max(1, maxDepth));
    rbTree.calculateCoordinates(rbTree.root, 0, 0, horizontalSpacing, verticalSpacing);

    // Set total steps
    setTotalSteps(rbTree.operations.length);

    // Draw initial tree
    drawRBTree(0);
  };

  // Build B-Tree
  const buildBTree = () => {
    bTree.root = bTree.createNode(true);
    bTree.operations = [];

    // Insert values one by one
    values.forEach(value => {
      bTree.insert(value);
    });

    // Calculate coordinates for visualization with better spacing
    const nodeCount = bTree.getAllNodes().length;

    // B-Trees need more appropriate spacing based on tree size
    const horizontalSpacing = Math.min(80, 400 / Math.max(1, Math.pow(2, Math.ceil(Math.log2(nodeCount)))));
    const verticalSpacing = Math.min(70, 300 / Math.max(1, Math.ceil(Math.log2(nodeCount))));
    bTree.calculateCoordinates(bTree.root, 0, 0, horizontalSpacing, verticalSpacing);

    // Set total steps
    setTotalSteps(bTree.operations.length);

    // Draw initial tree
    drawBTree(0);
  };

  // Start animation
  const startAnimation = () => {
    if (currentStep < totalSteps) {
      const timeout = setTimeout(() => {
        setCurrentStep(prev => {
          const nextStep = prev + 1;
          if (nextStep <= totalSteps) {
            drawTree(nextStep);
            return nextStep;
          }
          return prev;
        });
      }, 1000 / animationSpeed);

      setAnimationTimeout(timeout);
    }
  };

  // Stop animation
  const stopAnimation = () => {
    if (animationTimeout) {
      clearTimeout(animationTimeout);
      setAnimationTimeout(null);
    }
  };

  // Draw the tree based on the current step
  const drawTree = (step: number) => {
    switch (treeType) {
      case 'avl':
        drawAVLTree(step);
        break;
      case 'rb':
        drawRBTree(step);
        break;
      case 'btree':
        drawBTree(step);
        break;
    }
  };

  // Update operation description
  const updateOperationDescription = () => {
    if (currentStep < 1) {
      setOperationDescription('Initial state');
      return;
    }

    let description = '';

    switch (treeType) {
      case 'avl':
        description = avlTree.operations[currentStep - 1]?.description || '';
        break;
      case 'rb':
        description = rbTree.operations[currentStep - 1]?.description || '';
        break;
      case 'btree':
        description = bTree.operations[currentStep - 1]?.description || '';
        break;
    }

    setOperationDescription(description);
  };

  // Draw AVL Tree
  const drawAVLTree = (step: number) => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // If we're at step 0, just show an empty SVG
    if (step === 0 && values.length > 0) {
      setOperationDescription('Initial state - empty tree');
      return;
    }

    // Create a new tree for visualization
    const visualTree = new AVLTree();

    // Apply all values up to the current step
    // For a more accurate visualization, we should rebuild the tree for each step
    const valuesToInsert = [];
    let insertedValues = new Set<number>();

    // Process operations to determine which values to insert
    for (let i = 0; i < step; i++) {
      const op = avlTree.operations[i];
      if (op.type === 'insert' && op.node) {
        if (!insertedValues.has(op.node.value)) {
          valuesToInsert.push(op.node.value);
          insertedValues.add(op.node.value);
        }
      }
    }

    // Insert all values into the visualization tree
    valuesToInsert.forEach(value => {
      visualTree.insert(value);
    });

    // Calculate coordinates for visualization
    visualTree.calculateCoordinates(
      visualTree.root,
      0,
      0,
      Math.max(80, 800 / (Math.pow(2, Math.ceil(Math.log2(values.length))) + 1)),
      80
    );

    // Get nodes and edges
    const nodes = visualTree.getAllNodes();
    const edges = visualTree.getEdges();

    // Set viewBox to ensure the tree fits in the SVG and is centered
    const svgWidth = 900; // Much wider SVG
    const svgHeight = 400;

    // Find the bounds of the tree to center it
    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
    nodes.forEach(node => {
      minX = Math.min(minX, node.x || 0);
      maxX = Math.max(maxX, node.x || 0);
      minY = Math.min(minY, node.y || 0);
      maxY = Math.max(maxY, node.y || 0);
    });

    // Add padding with extra space on the left
    const paddingTop = 30;
    const paddingLeft = 250; // Even more padding on the left
    const paddingRight = 50;
    const paddingBottom = 30;

    minX = Math.max(0, minX - paddingLeft);
    minY = Math.max(0, minY - paddingTop);
    maxX = maxX + paddingRight;
    maxY = maxY + paddingBottom;

    // Calculate the width and height of the tree
    const treeWidth = maxX - minX;
    const treeHeight = maxY - minY;

    // Set viewBox to center the tree
    // If the tree is smaller than the SVG, center it
    // If the tree is larger than the SVG, scale it to fit
    let viewBoxWidth, viewBoxHeight, viewBoxX, viewBoxY;

    if (treeWidth <= svgWidth && treeHeight <= svgHeight) {
      // Tree fits within SVG, center it but shift more to the right and zoom in
      viewBoxWidth = svgWidth * 0.8; // Zoom in by reducing viewBox width
      viewBoxHeight = svgHeight * 0.8; // Zoom in by reducing viewBox height
      // Shift more to the right by reducing the left margin
      viewBoxX = Math.max(0, minX - (viewBoxWidth - treeWidth) / 4); // Use 1/4 instead of 1/3 to shift further right
      viewBoxY = minY - (viewBoxHeight - treeHeight) / 2;
    } else {
      // Tree is larger than SVG, scale it to fit and shift more to the right
      const scaleX = svgWidth / treeWidth;
      const scaleY = svgHeight / treeHeight;
      const scale = Math.min(scaleX, scaleY) * 0.8; // Zoom in by reducing scale by 20%

      viewBoxWidth = svgWidth / scale;
      viewBoxHeight = svgHeight / scale;
      // Shift more to the right by reducing the left margin
      viewBoxX = Math.max(0, minX - (viewBoxWidth - treeWidth) / 4); // Use 1/4 instead of 1/3 to shift further right
      viewBoxY = minY - (viewBoxHeight - treeHeight) / 2;
    }

    // Ensure viewBox doesn't go negative
    viewBoxX = Math.max(0, viewBoxX);
    viewBoxY = Math.max(0, viewBoxY);

    svg.attr('viewBox', `${viewBoxX} ${viewBoxY} ${viewBoxWidth} ${viewBoxHeight}`);

    // Draw edges
    edges.forEach(edge => {
      svg.append('line')
        .attr('x1', edge.source.x)
        .attr('y1', edge.source.y)
        .attr('x2', edge.target.x)
        .attr('y2', edge.target.y)
        .attr('stroke', '#999')
        .attr('stroke-width', 2);
    });

    // Draw nodes
    nodes.forEach(node => {
      const g = svg.append('g')
        .attr('transform', `translate(${node.x}, ${node.y})`);

      g.append('circle')
        .attr('r', 16)
        .attr('class', 'tree-node tree-node-avl')
        .attr('fill', '#4CAF50');

      g.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '0.3em')
        .attr('fill', 'white')
        .attr('font-size', '12px')
        .text(node.value);

      // Show height
      g.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '-1.2em')
        .attr('fill', '#333')
        .attr('font-size', '9px')
        .text(`h: ${node.height}`);
    });
  };

  // Draw Red-Black Tree
  const drawRBTree = (step: number) => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // If we're at step 0, just show an empty SVG
    if (step === 0 && values.length > 0) {
      setOperationDescription('Initial state - empty tree');
      return;
    }

    // Create a new tree for visualization
    const visualTree = new RedBlackTree();

    // Apply all values up to the current step
    const valuesToInsert = [];
    let insertedValues = new Set<number>();

    // Process operations to determine which values to insert
    for (let i = 0; i < step; i++) {
      const op = rbTree.operations[i];
      if (op.type === 'insert' && op.node) {
        if (!insertedValues.has(op.node.value)) {
          valuesToInsert.push(op.node.value);
          insertedValues.add(op.node.value);
        }
      }
    }

    // Insert all values into the visualization tree
    valuesToInsert.forEach(value => {
      visualTree.insert(value);
    });

    // Calculate coordinates for visualization with better spacing
    const maxDepth = getTreeDepth(visualTree.root);
    const nodeCount = visualTree.getAllNodes().length;

    // Use appropriate spacing based on tree size
    const horizontalSpacing = Math.min(60, 400 / Math.max(1, Math.pow(2, Math.min(maxDepth, 4))));
    const verticalSpacing = Math.min(60, 300 / Math.max(1, maxDepth));
    visualTree.calculateCoordinates(visualTree.root, 0, 0, horizontalSpacing, verticalSpacing);

    // Get nodes and edges
    const nodes = visualTree.getAllNodes();
    const edges = visualTree.getEdges();

    // Set viewBox to ensure the tree fits in the SVG and is centered
    const svgWidth = 900; // Much wider SVG
    const svgHeight = 400;

    // Find the bounds of the tree to center it
    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
    nodes.forEach(node => {
      minX = Math.min(minX, node.x || 0);
      maxX = Math.max(maxX, node.x || 0);
      minY = Math.min(minY, node.y || 0);
      maxY = Math.max(maxY, node.y || 0);
    });

    // Add padding with extra space on the left
    const paddingTop = 30;
    const paddingLeft = 250; // Even more padding on the left
    const paddingRight = 50;
    const paddingBottom = 30;

    minX = Math.max(0, minX - paddingLeft);
    minY = Math.max(0, minY - paddingTop);
    maxX = maxX + paddingRight;
    maxY = maxY + paddingBottom;

    // Calculate the width and height of the tree
    const treeWidth = maxX - minX;
    const treeHeight = maxY - minY;

    // Set viewBox to center the tree
    // If the tree is smaller than the SVG, center it
    // If the tree is larger than the SVG, scale it to fit
    let viewBoxWidth, viewBoxHeight, viewBoxX, viewBoxY;

    if (treeWidth <= svgWidth && treeHeight <= svgHeight) {
      // Tree fits within SVG, center it but shift more to the right and zoom in
      viewBoxWidth = svgWidth * 0.8; // Zoom in by reducing viewBox width
      viewBoxHeight = svgHeight * 0.8; // Zoom in by reducing viewBox height
      // Shift more to the right by reducing the left margin
      viewBoxX = Math.max(0, minX - (viewBoxWidth - treeWidth) / 4); // Use 1/4 instead of 1/3 to shift further right
      viewBoxY = minY - (viewBoxHeight - treeHeight) / 2;
    } else {
      // Tree is larger than SVG, scale it to fit and shift more to the right
      const scaleX = svgWidth / treeWidth;
      const scaleY = svgHeight / treeHeight;
      const scale = Math.min(scaleX, scaleY) * 0.8; // Zoom in by reducing scale by 20%

      viewBoxWidth = svgWidth / scale;
      viewBoxHeight = svgHeight / scale;
      // Shift more to the right by reducing the left margin
      viewBoxX = Math.max(0, minX - (viewBoxWidth - treeWidth) / 4); // Use 1/4 instead of 1/3 to shift further right
      viewBoxY = minY - (viewBoxHeight - treeHeight) / 2;
    }

    // Ensure viewBox doesn't go negative
    viewBoxX = Math.max(0, viewBoxX);
    viewBoxY = Math.max(0, viewBoxY);

    svg.attr('viewBox', `${viewBoxX} ${viewBoxY} ${viewBoxWidth} ${viewBoxHeight}`);

    // Draw edges
    edges.forEach(edge => {
      svg.append('line')
        .attr('x1', edge.source.x)
        .attr('y1', edge.source.y)
        .attr('x2', edge.target.x)
        .attr('y2', edge.target.y)
        .attr('stroke', '#999')
        .attr('stroke-width', 2);
    });

    // Draw nodes
    nodes.forEach(node => {
      const g = svg.append('g')
        .attr('transform', `translate(${node.x}, ${node.y})`);

      g.append('circle')
        .attr('r', 16)
        .attr('class', `tree-node ${node.color === Color.RED ? 'tree-node-rb-red' : 'tree-node-rb-black'}`)
        .attr('fill', node.color === Color.RED ? '#F44336' : '#212121');

      g.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '0.3em')
        .attr('fill', 'white')
        .attr('font-size', '12px')
        .text(node.value);
    });
  };

  // Draw B-Tree
  const drawBTree = (step: number) => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // If we're at step 0, just show an empty SVG
    if (step === 0 && values.length > 0) {
      setOperationDescription('Initial state - empty tree');
      return;
    }

    // Create a new tree for visualization
    const visualTree = new BTree(3); // B-Tree of order 3

    // Apply all values up to the current step
    const valuesToInsert = [];
    let insertedValues = new Set<number>();

    // Process operations to determine which values to insert
    for (let i = 0; i < step; i++) {
      const op = bTree.operations[i];
      if (op.type === 'insert' && op.key !== undefined) {
        if (!insertedValues.has(op.key)) {
          valuesToInsert.push(op.key);
          insertedValues.add(op.key);
        }
      }
    }

    // Insert all values into the visualization tree
    valuesToInsert.forEach(value => {
      visualTree.insert(value);
    });

    // Calculate coordinates for visualization with better spacing
    const maxDepth = getTreeDepth(visualTree.root);
    const nodeCount = visualTree.getAllNodes().length;

    // Use appropriate spacing based on tree size
    const horizontalSpacing = Math.min(80, 400 / Math.max(1, Math.pow(2, Math.ceil(Math.log2(nodeCount)))));
    const verticalSpacing = Math.min(70, 300 / Math.max(1, Math.ceil(Math.log2(nodeCount))));
    visualTree.calculateCoordinates(visualTree.root, 0, 0, horizontalSpacing, verticalSpacing);

    // Get nodes and edges
    const nodes = visualTree.getAllNodes();
    const edges = visualTree.getEdges();

    // Set viewBox to ensure the tree fits in the SVG and is centered
    const svgWidth = 900; // Much wider SVG
    const svgHeight = 400;

    // Find the bounds of the tree to center it
    let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
    nodes.forEach(node => {
      minX = Math.min(minX, node.x || 0);
      maxX = Math.max(maxX, node.x || 0);
      minY = Math.min(minY, node.y || 0);
      maxY = Math.max(maxY, node.y || 0);
    });

    // Add padding with extra space on the left
    const paddingTop = 30;
    const paddingLeft = 250; // Even more padding on the left
    const paddingRight = 50;
    const paddingBottom = 30;

    minX = Math.max(0, minX - paddingLeft);
    minY = Math.max(0, minY - paddingTop);
    maxX = maxX + paddingRight;
    maxY = maxY + paddingBottom;

    // Calculate the width and height of the tree
    const treeWidth = maxX - minX;
    const treeHeight = maxY - minY;

    // Set viewBox to center the tree
    // If the tree is smaller than the SVG, center it
    // If the tree is larger than the SVG, scale it to fit
    let viewBoxWidth, viewBoxHeight, viewBoxX, viewBoxY;

    if (treeWidth <= svgWidth && treeHeight <= svgHeight) {
      // Tree fits within SVG, center it but shift more to the right and zoom in
      viewBoxWidth = svgWidth * 0.8; // Zoom in by reducing viewBox width
      viewBoxHeight = svgHeight * 0.8; // Zoom in by reducing viewBox height
      // Shift more to the right by reducing the left margin
      viewBoxX = Math.max(0, minX - (viewBoxWidth - treeWidth) / 4); // Use 1/4 instead of 1/3 to shift further right
      viewBoxY = minY - (viewBoxHeight - treeHeight) / 2;
    } else {
      // Tree is larger than SVG, scale it to fit and shift more to the right
      const scaleX = svgWidth / treeWidth;
      const scaleY = svgHeight / treeHeight;
      const scale = Math.min(scaleX, scaleY) * 0.8; // Zoom in by reducing scale by 20%

      viewBoxWidth = svgWidth / scale;
      viewBoxHeight = svgHeight / scale;
      // Shift more to the right by reducing the left margin
      viewBoxX = Math.max(0, minX - (viewBoxWidth - treeWidth) / 4); // Use 1/4 instead of 1/3 to shift further right
      viewBoxY = minY - (viewBoxHeight - treeHeight) / 2;
    }

    // Ensure viewBox doesn't go negative
    viewBoxX = Math.max(0, viewBoxX);
    viewBoxY = Math.max(0, viewBoxY);

    svg.attr('viewBox', `${viewBoxX} ${viewBoxY} ${viewBoxWidth} ${viewBoxHeight}`);

    // Draw edges
    edges.forEach(edge => {
      svg.append('line')
        .attr('x1', edge.source.x)
        .attr('y1', edge.source.y)
        .attr('x2', edge.target.x)
        .attr('y2', edge.target.y)
        .attr('stroke', '#999')
        .attr('stroke-width', 2);
    });

    // Draw nodes
    nodes.forEach(node => {
      const g = svg.append('g')
        .attr('transform', `translate(${node.x}, ${node.y})`);

      // B-Tree nodes can have multiple keys
      const nodeWidth = Math.max(25 * node.keys.length, 25);

      g.append('rect')
        .attr('x', -nodeWidth / 2)
        .attr('y', -12)
        .attr('width', nodeWidth)
        .attr('height', 24)
        .attr('rx', 4)
        .attr('class', 'tree-node tree-node-btree')
        .attr('fill', '#2196F3');

      // Draw keys
      node.keys.forEach((key, i) => {
        const x = -nodeWidth / 2 + (i + 0.5) * (nodeWidth / node.keys.length);

        g.append('text')
          .attr('x', x)
          .attr('y', 4)
          .attr('text-anchor', 'middle')
          .attr('fill', 'white')
          .attr('font-size', '11px')
          .text(key);
      });
    });
  };

  return (
    <div className="relative">
      <svg
        ref={svgRef}
        width="100%"
        height="500"
        className="border border-gray-300 rounded-lg bg-white dark:bg-gray-800 min-h-[500px]"
      />
      <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
        <h3 className="font-semibold mb-2">Operation:</h3>
        <p>{operationDescription || 'No operation'}</p>
      </div>
    </div>
  );
};

export default TreeVisualizer;
