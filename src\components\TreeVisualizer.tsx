import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { AVLTree, AVLNode } from '../data/AVLTree';
import { RedBlackTree, RBNode, Color } from '../data/RedBlackTree';
import { BTree, BTreeNode } from '../data/BTree';

type TreeType = 'avl' | 'rb' | 'btree';

interface TreeVisualizerProps {
  treeType: TreeType;
  values: number[];
  animationSpeed: number;
  isPlaying: boolean;
  onStepChange: (currentStep: number, totalSteps: number) => void;
}

const TreeVisualizer: React.FC<TreeVisualizerProps> = ({
  treeType,
  values,
  animationSpeed,
  isPlaying,
  onStepChange,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [totalSteps, setTotalSteps] = useState(0);
  const [avlTree] = useState(new AVLTree());
  const [rbTree] = useState(new RedBlackTree());
  const [bTree] = useState(new BTree(3)); // B-Tree of order 3
  const [operationDescription, setOperationDescription] = useState('');
  const [animationTimeout, setAnimationTimeout] = useState<NodeJS.Timeout | null>(null);

  // Reset and build tree when values change
  useEffect(() => {
    resetTree();
    if (values.length > 0) {
      buildTree();
    }
  }, [values, treeType]);

  // Handle animation when isPlaying changes
  useEffect(() => {
    if (isPlaying) {
      startAnimation();
    } else {
      stopAnimation();
    }
  }, [isPlaying, animationSpeed, currentStep]);

  // Update step info when current step changes
  useEffect(() => {
    onStepChange(currentStep, totalSteps);
    updateOperationDescription();
  }, [currentStep, totalSteps]);

  // Reset the tree
  const resetTree = () => {
    if (animationTimeout) {
      clearTimeout(animationTimeout);
      setAnimationTimeout(null);
    }
    setCurrentStep(0);
    setTotalSteps(0);
    setOperationDescription('');
    
    // Clear the SVG
    if (svgRef.current) {
      d3.select(svgRef.current).selectAll('*').remove();
    }
  };

  // Build the tree step by step
  const buildTree = () => {
    switch (treeType) {
      case 'avl':
        buildAVLTree();
        break;
      case 'rb':
        buildRBTree();
        break;
      case 'btree':
        buildBTree();
        break;
    }
  };

  // Build AVL Tree
  const buildAVLTree = () => {
    avlTree.root = null;
    avlTree.operations = [];
    
    // Insert values one by one
    values.forEach(value => {
      avlTree.insert(value);
    });
    
    // Calculate coordinates for visualization
    avlTree.calculateCoordinates();
    
    // Set total steps
    setTotalSteps(avlTree.operations.length);
    
    // Draw initial tree
    drawAVLTree(0);
  };

  // Build Red-Black Tree
  const buildRBTree = () => {
    rbTree.root = rbTree.NIL;
    rbTree.operations = [];
    
    // Insert values one by one
    values.forEach(value => {
      rbTree.insert(value);
    });
    
    // Calculate coordinates for visualization
    rbTree.calculateCoordinates();
    
    // Set total steps
    setTotalSteps(rbTree.operations.length);
    
    // Draw initial tree
    drawRBTree(0);
  };

  // Build B-Tree
  const buildBTree = () => {
    bTree.root = bTree.createNode(true);
    bTree.operations = [];
    
    // Insert values one by one
    values.forEach(value => {
      bTree.insert(value);
    });
    
    // Calculate coordinates for visualization
    bTree.calculateCoordinates();
    
    // Set total steps
    setTotalSteps(bTree.operations.length);
    
    // Draw initial tree
    drawBTree(0);
  };

  // Start animation
  const startAnimation = () => {
    if (currentStep < totalSteps) {
      const timeout = setTimeout(() => {
        setCurrentStep(prev => {
          const nextStep = prev + 1;
          if (nextStep < totalSteps) {
            drawTree(nextStep);
            return nextStep;
          }
          return prev;
        });
      }, 1000 / animationSpeed);
      
      setAnimationTimeout(timeout);
    }
  };

  // Stop animation
  const stopAnimation = () => {
    if (animationTimeout) {
      clearTimeout(animationTimeout);
      setAnimationTimeout(null);
    }
  };

  // Draw the tree based on the current step
  const drawTree = (step: number) => {
    switch (treeType) {
      case 'avl':
        drawAVLTree(step);
        break;
      case 'rb':
        drawRBTree(step);
        break;
      case 'btree':
        drawBTree(step);
        break;
    }
  };

  // Update operation description
  const updateOperationDescription = () => {
    if (currentStep < 1) {
      setOperationDescription('Initial state');
      return;
    }
    
    let description = '';
    
    switch (treeType) {
      case 'avl':
        description = avlTree.operations[currentStep - 1]?.description || '';
        break;
      case 'rb':
        description = rbTree.operations[currentStep - 1]?.description || '';
        break;
      case 'btree':
        description = bTree.operations[currentStep - 1]?.description || '';
        break;
    }
    
    setOperationDescription(description);
  };

  // Draw AVL Tree
  const drawAVLTree = (step: number) => {
    if (!svgRef.current) return;
    
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();
    
    // Create a temporary tree to visualize the state at the current step
    const tempTree = new AVLTree();
    tempTree.root = null;
    
    // Apply operations up to the current step
    for (let i = 0; i < step; i++) {
      const op = avlTree.operations[i];
      if (op.type === 'insert' && op.node) {
        if (!tempTree.root) {
          tempTree.root = { ...op.node, left: null, right: null };
        } else {
          // Simple insertion without balancing for visualization
          const insertNode = (node: AVLNode, value: number): AVLNode => {
            if (!node) {
              return { ...op.node, left: null, right: null };
            }
            
            if (value < node.value) {
              node.left = insertNode(node.left, value);
            } else if (value > node.value) {
              node.right = insertNode(node.right, value);
            }
            
            return node;
          };
          
          insertNode(tempTree.root, op.node.value);
        }
      }
      // For simplicity, we're not implementing the full AVL balancing here
      // In a real implementation, you would need to handle rotations as well
    }
    
    // Calculate coordinates for the temporary tree
    tempTree.calculateCoordinates();
    
    // Get nodes and edges
    const nodes = tempTree.getAllNodes();
    const edges = tempTree.getEdges();
    
    // Draw edges
    edges.forEach(edge => {
      svg.append('line')
        .attr('x1', edge.source.x)
        .attr('y1', edge.source.y)
        .attr('x2', edge.target.x)
        .attr('y2', edge.target.y)
        .attr('stroke', '#999')
        .attr('stroke-width', 2);
    });
    
    // Draw nodes
    nodes.forEach(node => {
      const g = svg.append('g')
        .attr('transform', `translate(${node.x}, ${node.y})`);
      
      g.append('circle')
        .attr('r', 20)
        .attr('class', 'tree-node tree-node-avl')
        .attr('fill', '#4CAF50');
      
      g.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '0.3em')
        .attr('fill', 'white')
        .text(node.value);
      
      // Show height
      g.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '-1.5em')
        .attr('fill', '#333')
        .attr('font-size', '10px')
        .text(`h: ${node.height}`);
    });
  };

  // Draw Red-Black Tree
  const drawRBTree = (step: number) => {
    if (!svgRef.current) return;
    
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();
    
    // Create a temporary tree to visualize the state at the current step
    const tempTree = new RedBlackTree();
    
    // Apply operations up to the current step
    // This is a simplified version for visualization
    // In a real implementation, you would need to handle all RB tree operations
    
    // Get nodes and edges from the original tree
    // For simplicity, we're using the final state of the tree
    rbTree.calculateCoordinates();
    const nodes = rbTree.getAllNodes();
    const edges = rbTree.getEdges();
    
    // Draw edges
    edges.forEach(edge => {
      svg.append('line')
        .attr('x1', edge.source.x)
        .attr('y1', edge.source.y)
        .attr('x2', edge.target.x)
        .attr('y2', edge.target.y)
        .attr('stroke', '#999')
        .attr('stroke-width', 2);
    });
    
    // Draw nodes
    nodes.forEach(node => {
      const g = svg.append('g')
        .attr('transform', `translate(${node.x}, ${node.y})`);
      
      g.append('circle')
        .attr('r', 20)
        .attr('class', `tree-node ${node.color === Color.RED ? 'tree-node-rb-red' : 'tree-node-rb-black'}`)
        .attr('fill', node.color === Color.RED ? '#F44336' : '#212121');
      
      g.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '0.3em')
        .attr('fill', 'white')
        .text(node.value);
    });
  };

  // Draw B-Tree
  const drawBTree = (step: number) => {
    if (!svgRef.current) return;
    
    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();
    
    // Create a temporary tree to visualize the state at the current step
    // This is a simplified version for visualization
    
    // Get nodes and edges from the original tree
    // For simplicity, we're using the final state of the tree
    bTree.calculateCoordinates();
    const nodes = bTree.getAllNodes();
    const edges = bTree.getEdges();
    
    // Draw edges
    edges.forEach(edge => {
      svg.append('line')
        .attr('x1', edge.source.x)
        .attr('y1', edge.source.y)
        .attr('x2', edge.target.x)
        .attr('y2', edge.target.y)
        .attr('stroke', '#999')
        .attr('stroke-width', 2);
    });
    
    // Draw nodes
    nodes.forEach(node => {
      const g = svg.append('g')
        .attr('transform', `translate(${node.x}, ${node.y})`);
      
      // B-Tree nodes can have multiple keys
      const nodeWidth = Math.max(30 * node.keys.length, 30);
      
      g.append('rect')
        .attr('x', -nodeWidth / 2)
        .attr('y', -15)
        .attr('width', nodeWidth)
        .attr('height', 30)
        .attr('rx', 5)
        .attr('class', 'tree-node tree-node-btree')
        .attr('fill', '#2196F3');
      
      // Draw keys
      node.keys.forEach((key, i) => {
        const x = -nodeWidth / 2 + (i + 0.5) * (nodeWidth / node.keys.length);
        
        g.append('text')
          .attr('x', x)
          .attr('y', 5)
          .attr('text-anchor', 'middle')
          .attr('fill', 'white')
          .text(key);
      });
    });
  };

  return (
    <div className="relative">
      <svg 
        ref={svgRef} 
        width="100%" 
        height="500" 
        className="border border-gray-300 rounded-lg bg-white dark:bg-gray-800"
      />
      <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
        <h3 className="font-semibold mb-2">Operation:</h3>
        <p>{operationDescription || 'No operation'}</p>
      </div>
    </div>
  );
};

export default TreeVisualizer;
