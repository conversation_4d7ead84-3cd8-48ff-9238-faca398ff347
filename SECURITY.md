# Security Policy

## Supported Versions

We currently support the following versions with security updates:

| Version | Supported          |
| ------- | ------------------ |
| 1.0.x   | :white_check_mark: |

## Reporting a Vulnerability

If you discover a security vulnerability within Treezy, please send an email to [<EMAIL>]. All security vulnerabilities will be promptly addressed.

Please include the following information in your report:
- Type of issue (e.g., buffer overflow, SQL injection, cross-site scripting, etc.)
- Full paths of source file(s) related to the manifestation of the issue
- The location of the affected source code (tag/branch/commit or direct URL)
- Any special configuration required to reproduce the issue
- Step-by-step instructions to reproduce the issue
- Proof-of-concept or exploit code (if possible)
- Impact of the issue, including how an attacker might exploit it

## Security Measures

- All dependencies are regularly updated
- Code is reviewed for security vulnerabilities
- Production builds are minified and optimized
- HTTPS is enforced on all deployments
- Input validation is implemented for all user inputs
- XSS protection is enabled
- CSRF protection is implemented

## Best Practices

When using Treezy:
1. Always use the latest version
2. Report any security issues immediately
3. Follow the installation instructions carefully
4. Keep your development environment secure 