// A simplified Red-Black Tree implementation focused on visualization
export enum SimpleColor {
  RED = 'RED',
  BLACK = 'BLACK'
}

export interface SimpleRBNode {
  value: number;
  color: SimpleColor;
  left: SimpleRBNode | null;
  right: SimpleRBNode | null;
  parent: SimpleRBNode | null;
  x?: number;
  y?: number;
}

export interface RBOperation {
  type: 'insert';
  value: number;
  description: string;
}

export class SimpleRBTree {
  root: SimpleRBNode | null;
  NIL: SimpleRBNode;
  operations: RBOperation[];

  constructor() {
    // Create NIL node (sentinel)
    this.NIL = {
      value: 0,
      color: SimpleColor.BLACK,
      left: null,
      right: null,
      parent: null
    };

    this.root = this.NIL;
    this.operations = [];
  }

  // Insert a value into the Red-Black Tree
  insert(value: number): void {
    // Add operation to track this insertion
    this.operations.push({
      type: 'insert',
      value: value,
      description: `Insert ${value} into the tree`
    });

    // Create a new node
    const newNode: SimpleRBNode = {
      value,
      color: SimpleColor.RED,
      left: this.NIL,
      right: this.NIL,
      parent: null
    };

    // If tree is empty, make the new node the root
    if (this.root === this.NIL) {
      newNode.color = SimpleColor.BLACK;
      this.root = newNode;
      return;
    }

    // Find the position to insert the new node
    let current = this.root;
    let parent = null;

    while (current !== this.NIL) {
      parent = current;

      if (value < current.value) {
        current = current.left!;
      } else {
        current = current.right!;
      }
    }

    // Set the parent of the new node
    newNode.parent = parent;

    // Insert the new node as a child of the parent
    if (parent) {
      if (value < parent.value) {
        parent.left = newNode;
      } else {
        parent.right = newNode;
      }
    }

    // Fix the Red-Black Tree properties
    this.fixInsert(newNode);
  }

  // Fix the Red-Black Tree properties after insertion
  private fixInsert(node: SimpleRBNode): void {
    let current = node;

    // Fix the tree until we reach the root or the parent is black
    while (current !== this.root && current.parent && current.parent.color === SimpleColor.RED) {
      // If parent is the left child of grandparent
      if (current.parent.parent && current.parent === current.parent.parent.left) {
        const uncle = current.parent.parent.right;

        // Case 1: Uncle is red
        if (uncle && uncle.color === SimpleColor.RED) {
          current.parent.color = SimpleColor.BLACK;
          uncle.color = SimpleColor.BLACK;
          current.parent.parent.color = SimpleColor.RED;
          current = current.parent.parent;
        } else {
          // Case 2: Uncle is black and current is right child
          if (current === current.parent.right) {
            current = current.parent;
            this.leftRotate(current);
          }

          // Case 3: Uncle is black and current is left child
          if (current.parent && current.parent.parent) {
            current.parent.color = SimpleColor.BLACK;
            current.parent.parent.color = SimpleColor.RED;
            this.rightRotate(current.parent.parent);
          }
        }
      } else { // Parent is the right child of grandparent
        const uncle = current.parent.parent!.left;

        // Case 1: Uncle is red
        if (uncle && uncle.color === SimpleColor.RED) {
          current.parent.color = SimpleColor.BLACK;
          uncle.color = SimpleColor.BLACK;
          current.parent.parent!.color = SimpleColor.RED;
          current = current.parent.parent!;
        } else {
          // Case 2: Uncle is black and current is left child
          if (current === current.parent.left) {
            current = current.parent;
            this.rightRotate(current);
          }

          // Case 3: Uncle is black and current is right child
          if (current.parent && current.parent.parent) {
            current.parent.color = SimpleColor.BLACK;
            current.parent.parent.color = SimpleColor.RED;
            this.leftRotate(current.parent.parent);
          }
        }
      }
    }

    // Ensure the root is black
    if (this.root) {
      this.root.color = SimpleColor.BLACK;
    }
  }

  // Left rotation
  private leftRotate(x: SimpleRBNode): void {
    const y = x.right!;

    // Turn y's left subtree into x's right subtree
    x.right = y.left;

    if (y.left !== this.NIL) {
      y.left.parent = x;
    }

    // Link x's parent to y
    y.parent = x.parent;

    if (x.parent === null) {
      this.root = y;
    } else if (x === x.parent.left) {
      x.parent.left = y;
    } else {
      x.parent.right = y;
    }

    // Put x on y's left
    y.left = x;
    x.parent = y;
  }

  // Right rotation
  private rightRotate(y: SimpleRBNode): void {
    const x = y.left!;

    // Turn x's right subtree into y's left subtree
    y.left = x.right;

    if (x.right !== this.NIL) {
      x.right.parent = y;
    }

    // Link y's parent to x
    x.parent = y.parent;

    if (y.parent === null) {
      this.root = x;
    } else if (y === y.parent.left) {
      y.parent.left = x;
    } else {
      y.parent.right = x;
    }

    // Put y on x's right
    x.right = y;
    y.parent = x;
  }

  // Calculate coordinates for visualization
  calculateCoordinates(): void {
    if (!this.root || this.root === this.NIL) return;

    // First pass: assign y-coordinates based on depth
    this.assignYCoordinates(this.root, 0);

    // Second pass: assign x-coordinates
    this.assignXCoordinates(this.root, 400);

    // Third pass: center each level
    this.centerEachLevel();
  }

  // First pass: assign y-coordinates based on depth
  private assignYCoordinates(node: SimpleRBNode, depth: number): void {
    if (!node || node === this.NIL) return;

    // Set y-coordinate based on depth
    node.y = depth * 60 + 40;

    // Process children
    this.assignYCoordinates(node.left!, depth + 1);
    this.assignYCoordinates(node.right!, depth + 1);
  }

  // Second pass: assign x-coordinates
  private assignXCoordinates(node: SimpleRBNode, startX: number): number {
    if (!node || node === this.NIL) return startX;

    // Process left subtree
    let currentX = this.assignXCoordinates(node.left!, startX);

    // Assign x-coordinate to this node
    node.x = currentX;
    currentX += 60;

    // Process right subtree
    return this.assignXCoordinates(node.right!, currentX);
  }

  // Third pass: center each level
  private centerEachLevel(): void {
    // Group nodes by level (y-coordinate)
    const nodesByLevel: Map<number, SimpleRBNode[]> = new Map();

    // Collect all nodes
    const allNodes = this.getAllNodes();

    // Group by y-coordinate
    allNodes.forEach(node => {
      const y = node.y || 0;
      if (!nodesByLevel.has(y)) {
        nodesByLevel.set(y, []);
      }
      nodesByLevel.get(y)?.push(node);
    });

    // Center each level
    nodesByLevel.forEach(nodes => {
      // Find min and max x on this level
      const minX = Math.min(...nodes.map(n => n.x || 0));
      const maxX = Math.max(...nodes.map(n => n.x || 0));

      // Calculate center of this level
      const center = (minX + maxX) / 2;

      // Shift all nodes to center around 400
      const offset = 400 - center;

      // Apply offset to all nodes on this level
      nodes.forEach(node => {
        if (node.x !== undefined) {
          node.x += offset;
        }
      });
    });
  }

  // Get all nodes for visualization
  getAllNodes(): SimpleRBNode[] {
    const nodes: SimpleRBNode[] = [];

    const traverse = (node: SimpleRBNode | null) => {
      if (!node || node === this.NIL) return;

      nodes.push(node);
      traverse(node.left);
      traverse(node.right);
    };

    traverse(this.root);
    return nodes;
  }

  // Get all edges for visualization
  getEdges(): { source: SimpleRBNode; target: SimpleRBNode }[] {
    const edges: { source: SimpleRBNode; target: SimpleRBNode }[] = [];

    const traverse = (node: SimpleRBNode | null) => {
      if (!node || node === this.NIL) return;

      if (node.left && node.left !== this.NIL) {
        edges.push({ source: node, target: node.left });
        traverse(node.left);
      }

      if (node.right && node.right !== this.NIL) {
        edges.push({ source: node, target: node.right });
        traverse(node.right);
      }
    };

    traverse(this.root);
    return edges;
  }
}
