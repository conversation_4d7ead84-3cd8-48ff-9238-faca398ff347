import {
  require_wren
} from "./chunk-6Y5267MM.js";
import {
  require_xeora
} from "./chunk-RW243E3Q.js";
import {
  require_xml_doc
} from "./chunk-LUQFUSIP.js";
import {
  require_xojo
} from "./chunk-HRLAMF4G.js";
import {
  require_xquery
} from "./chunk-YXTEZ5FR.js";
import {
  require_yang
} from "./chunk-ROST6G6V.js";
import {
  require_zig
} from "./chunk-QD6YJL6I.js";
import {
  require_core
} from "./chunk-TWUXFBT7.js";
import {
  require_vhdl
} from "./chunk-VM3WUF7X.js";
import {
  require_vim
} from "./chunk-45HQHT7V.js";
import {
  require_visual_basic
} from "./chunk-MARYMWLL.js";
import {
  require_warpscript
} from "./chunk-PMIY3YKG.js";
import {
  require_wasm
} from "./chunk-PIOE6YPJ.js";
import {
  require_web_idl
} from "./chunk-7YEHODOT.js";
import {
  require_wiki
} from "./chunk-DVYFF4KD.js";
import {
  require_wolfram
} from "./chunk-SIRY5DFS.js";
import {
  require_typoscript
} from "./chunk-UC3ESYBV.js";
import {
  require_unrealscript
} from "./chunk-YHAWVG47.js";
import {
  require_uorazor
} from "./chunk-IWEBHIUM.js";
import {
  require_uri
} from "./chunk-HV5E3QAB.js";
import {
  require_v
} from "./chunk-FFHLRBLG.js";
import {
  require_vala
} from "./chunk-SDQCWUSF.js";
import {
  require_velocity
} from "./chunk-BHHKT26R.js";
import {
  require_verilog
} from "./chunk-GMD6R7QZ.js";
import {
  require_tap
} from "./chunk-DZ746EGZ.js";
import {
  require_tcl
} from "./chunk-NHIIIBNG.js";
import {
  require_textile
} from "./chunk-NWOXNMOE.js";
import {
  require_toml
} from "./chunk-R5XMHTQ4.js";
import {
  require_tremor
} from "./chunk-C6PWKLQ7.js";
import {
  require_tsx
} from "./chunk-JRHW3BOD.js";
import {
  require_tt2
} from "./chunk-73YJU63U.js";
import {
  require_twig
} from "./chunk-DZ7TVAGF.js";
import {
  require_stylus
} from "./chunk-KLMPZL5S.js";
import {
  require_swift
} from "./chunk-HXLH5FGT.js";
import {
  require_systemd
} from "./chunk-OZFBGB5J.js";
import {
  require_t4_cs
} from "./chunk-CRW7OIH3.js";
import {
  require_t4_vb
} from "./chunk-AP7WP6RE.js";
import {
  require_t4_templating
} from "./chunk-GYLHWFHX.js";
import {
  require_vbnet
} from "./chunk-WQLC6LG7.js";
import {
  require_yaml
} from "./chunk-AI5FGDDE.js";
import {
  require_solution_file
} from "./chunk-DUJWX6LF.js";
import {
  require_soy
} from "./chunk-R4STC3LQ.js";
import {
  require_sparql
} from "./chunk-2HUIZO4A.js";
import {
  require_turtle
} from "./chunk-VPHH7ZIA.js";
import {
  require_splunk_spl
} from "./chunk-GEK3IGD3.js";
import {
  require_sqf
} from "./chunk-C63PN2FV.js";
import {
  require_squirrel
} from "./chunk-XBROU43M.js";
import {
  require_stan
} from "./chunk-NLKG2MI3.js";
import {
  require_scala
} from "./chunk-3ALKDBMK.js";
import {
  require_scss
} from "./chunk-L3QKZ2EB.js";
import {
  require_shell_session
} from "./chunk-2QDYLTBU.js";
import {
  require_smali
} from "./chunk-W2V6QPC5.js";
import {
  require_smalltalk
} from "./chunk-Y5O6G33S.js";
import {
  require_smarty
} from "./chunk-WI6767UZ.js";
import {
  require_sml
} from "./chunk-RU65MQJA.js";
import {
  require_solidity
} from "./chunk-DBLEKDXO.js";
import {
  require_renpy
} from "./chunk-LF5VDXZQ.js";
import {
  require_rest
} from "./chunk-AADELCKR.js";
import {
  require_rip
} from "./chunk-PAQUGN2C.js";
import {
  require_roboconf
} from "./chunk-DFPO4E3H.js";
import {
  require_robotframework
} from "./chunk-V7HSNVPP.js";
import {
  require_rust
} from "./chunk-6MJEGEL2.js";
import {
  require_sas
} from "./chunk-SQZKMIRI.js";
import {
  require_sass
} from "./chunk-3Z5DRCEE.js";
import {
  require_qml
} from "./chunk-X3QDDKWE.js";
import {
  require_qore
} from "./chunk-MCLI34HB.js";
import {
  require_qsharp
} from "./chunk-GTR26IGL.js";
import {
  require_r
} from "./chunk-TMXSNCE2.js";
import {
  require_racket
} from "./chunk-C3K5ALT7.js";
import {
  require_reason
} from "./chunk-LXI4A4LB.js";
import {
  require_regex
} from "./chunk-KQ4ZCZNR.js";
import {
  require_rego
} from "./chunk-KWL33U7D.js";
import {
  require_psl
} from "./chunk-OT53VKEX.js";
import {
  require_pug
} from "./chunk-BMUDGRJP.js";
import {
  require_puppet
} from "./chunk-NXML5CV4.js";
import {
  require_pure
} from "./chunk-7JYYLP35.js";
import {
  require_purebasic
} from "./chunk-4YS6NDYL.js";
import {
  require_purescript
} from "./chunk-FJMAK37K.js";
import {
  require_python
} from "./chunk-N2EXZGPG.js";
import {
  require_q
} from "./chunk-UX3HROGS.js";
import {
  require_plsql
} from "./chunk-PCDG3MAF.js";
import {
  require_powerquery
} from "./chunk-YHOUDMYM.js";
import {
  require_powershell
} from "./chunk-5MHD36T5.js";
import {
  require_processing
} from "./chunk-AI4U2GYW.js";
import {
  require_prolog
} from "./chunk-V7UMYMHA.js";
import {
  require_promql
} from "./chunk-UESXBGE4.js";
import {
  require_properties
} from "./chunk-WNCLCPTW.js";
import {
  require_protobuf
} from "./chunk-JYPQGDST.js";
import {
  require_parser
} from "./chunk-VS3N3X5I.js";
import {
  require_pascal
} from "./chunk-5KFORUXZ.js";
import {
  require_pascaligo
} from "./chunk-KNW5JWGP.js";
import {
  require_pcaxis
} from "./chunk-DQNZF23B.js";
import {
  require_peoplecode
} from "./chunk-EXAPQLKE.js";
import {
  require_perl
} from "./chunk-AOS6B3EE.js";
import {
  require_php_extras
} from "./chunk-OKTABKKP.js";
import {
  require_phpdoc
} from "./chunk-2RUMUXXS.js";
import {
  require_nix
} from "./chunk-6EXCS6Z4.js";
import {
  require_nsis
} from "./chunk-TFY4MH5W.js";
import {
  require_objectivec
} from "./chunk-53YOQ6RE.js";
import {
  require_ocaml
} from "./chunk-A54QC7IK.js";
import {
  require_opencl
} from "./chunk-FDFOPETF.js";
import {
  require_openqasm
} from "./chunk-EKIBIWQP.js";
import {
  require_oz
} from "./chunk-RISJXK4N.js";
import {
  require_parigp
} from "./chunk-27JVRQ3C.js";
import {
  require_n4js
} from "./chunk-ZGRJ5FZB.js";
import {
  require_nand2tetris_hdl
} from "./chunk-DCJRAGGW.js";
import {
  require_naniscript
} from "./chunk-57NF5NAT.js";
import {
  require_nasm
} from "./chunk-TPEXW4YI.js";
import {
  require_neon
} from "./chunk-UMZ53S3Q.js";
import {
  require_nevod
} from "./chunk-PMSENVPJ.js";
import {
  require_nginx
} from "./chunk-J3ATIOKO.js";
import {
  require_nim
} from "./chunk-FZ637HEG.js";
import {
  require_maxscript
} from "./chunk-723ECU77.js";
import {
  require_mel
} from "./chunk-MUDI2JWD.js";
import {
  require_mermaid
} from "./chunk-M6ZA7PQB.js";
import {
  require_mizar
} from "./chunk-KLDHWMD3.js";
import {
  require_mongodb
} from "./chunk-YB3PTGMA.js";
import {
  require_monkey
} from "./chunk-TVSIQMRV.js";
import {
  require_moonscript
} from "./chunk-WC7FQDDY.js";
import {
  require_n1ql
} from "./chunk-AGCD4EZ3.js";
import {
  require_llvm
} from "./chunk-ZSDQHKDN.js";
import {
  require_log
} from "./chunk-3KT35EIZ.js";
import {
  require_lolcode
} from "./chunk-6PXMQXIZ.js";
import {
  require_magma
} from "./chunk-2FO3WZQB.js";
import {
  require_makefile
} from "./chunk-YVZDFSJH.js";
import {
  require_markdown
} from "./chunk-KAJP6CWP.js";
import {
  require_matlab
} from "./chunk-IEPBN5E3.js";
import {
  require_latte
} from "./chunk-S3JU7ROV.js";
import {
  require_php
} from "./chunk-B6NAKQ7H.js";
import {
  require_less
} from "./chunk-QBC6SVNA.js";
import {
  require_lilypond
} from "./chunk-P52JG3IY.js";
import {
  require_scheme
} from "./chunk-CJHOGRLM.js";
import {
  require_liquid
} from "./chunk-XHVTIUSJ.js";
import {
  require_lisp
} from "./chunk-7SMAH4WP.js";
import {
  require_livescript
} from "./chunk-3VSZ4AEY.js";
import {
  require_jsx
} from "./chunk-UVUJEFAM.js";
import {
  require_julia
} from "./chunk-SSN3MEDZ.js";
import {
  require_keepalived
} from "./chunk-ZQPQPVHB.js";
import {
  require_keyman
} from "./chunk-J6BR3DEX.js";
import {
  require_kotlin
} from "./chunk-2X2YBCNF.js";
import {
  require_kumir
} from "./chunk-N6MRVP4W.js";
import {
  require_kusto
} from "./chunk-P33AZZIJ.js";
import {
  require_latex
} from "./chunk-U2WDS5MF.js";
import {
  require_js_extras
} from "./chunk-6ZLS7N76.js";
import {
  require_js_templates
} from "./chunk-XSXJAU6R.js";
import {
  require_jsdoc
} from "./chunk-X45WR6CY.js";
import {
  require_typescript
} from "./chunk-TWAMLFRZ.js";
import {
  require_json5
} from "./chunk-NFCRRDSY.js";
import {
  require_jsonp
} from "./chunk-E52TKBEA.js";
import {
  require_json
} from "./chunk-5WHL3RI2.js";
import {
  require_jsstacktrace
} from "./chunk-4MJQNSBD.js";
import {
  require_javadoc
} from "./chunk-VJDH62BH.js";
import {
  require_java
} from "./chunk-CXDY7GHY.js";
import {
  require_javadoclike
} from "./chunk-DUA3HAZ3.js";
import {
  require_javastacktrace
} from "./chunk-3VLFS3QN.js";
import {
  require_jexl
} from "./chunk-JFPNTSNK.js";
import {
  require_jolie
} from "./chunk-46GOSV5Z.js";
import {
  require_jq
} from "./chunk-H4MX4DOB.js";
import {
  require_icu_message_format
} from "./chunk-YZ4T72PS.js";
import {
  require_idris
} from "./chunk-DCJX6QOG.js";
import {
  require_iecst
} from "./chunk-LSUKWBQJ.js";
import {
  require_ignore
} from "./chunk-BTE7XZAL.js";
import {
  require_inform7
} from "./chunk-Q7LIIT7C.js";
import {
  require_ini
} from "./chunk-3RQKCSOZ.js";
import {
  require_io
} from "./chunk-77AHNTP2.js";
import {
  require_j
} from "./chunk-4XJ74RKP.js";
import {
  require_hcl
} from "./chunk-PK2MKQEA.js";
import {
  require_hlsl
} from "./chunk-NA6RBXUZ.js";
import {
  require_hoon
} from "./chunk-27EIFJO6.js";
import {
  require_hpkp
} from "./chunk-PZIIV4YB.js";
import {
  require_hsts
} from "./chunk-VT4BYVST.js";
import {
  require_http
} from "./chunk-LC2SYIOH.js";
import {
  require_ichigojam
} from "./chunk-JS2LXKTF.js";
import {
  require_icon
} from "./chunk-IXHVMXHY.js";
import {
  require_go_module
} from "./chunk-QN4KXCFK.js";
import {
  require_go
} from "./chunk-I4D675KU.js";
import {
  require_graphql
} from "./chunk-6I72KOSU.js";
import {
  require_groovy
} from "./chunk-SZRFSW5P.js";
import {
  require_haml
} from "./chunk-SJCRHZIU.js";
import {
  require_handlebars
} from "./chunk-HAI764EX.js";
import {
  require_haskell
} from "./chunk-HRRJPVNO.js";
import {
  require_haxe
} from "./chunk-XTRQVHBQ.js";
import {
  require_gcode
} from "./chunk-MYOP2DQX.js";
import {
  require_gdscript
} from "./chunk-6UVM3E6D.js";
import {
  require_gedcom
} from "./chunk-B4HJEEUD.js";
import {
  require_gherkin
} from "./chunk-Q53QBRU6.js";
import {
  require_git
} from "./chunk-PXBT4N3L.js";
import {
  require_glsl
} from "./chunk-GLK6J2VE.js";
import {
  require_gml
} from "./chunk-PQJYSHNB.js";
import {
  require_gn
} from "./chunk-ER6GSKKR.js";
import {
  require_factor
} from "./chunk-G5U7W7NB.js";
import {
  require_false
} from "./chunk-62STUU6H.js";
import {
  require_firestore_security_rules
} from "./chunk-2CLP73TF.js";
import {
  require_flow
} from "./chunk-UFZKWJOK.js";
import {
  require_fortran
} from "./chunk-72PVJQSJ.js";
import {
  require_fsharp
} from "./chunk-JWHVMCJV.js";
import {
  require_ftl
} from "./chunk-KD3BDM5F.js";
import {
  require_gap
} from "./chunk-HGQ2LJFO.js";
import {
  require_ejs
} from "./chunk-IXN2NVET.js";
import {
  require_elixir
} from "./chunk-VCRZ7CED.js";
import {
  require_elm
} from "./chunk-4LMNRHPJ.js";
import {
  require_erb
} from "./chunk-NFL4RO3Q.js";
import {
  require_erlang
} from "./chunk-7JW7WQQY.js";
import {
  require_etlua
} from "./chunk-SW6EDSBT.js";
import {
  require_lua
} from "./chunk-Y7JGIDJX.js";
import {
  require_excel_formula
} from "./chunk-COMIBGSN.js";
import {
  require_django
} from "./chunk-7G2VXFHQ.js";
import {
  require_markup_templating
} from "./chunk-I3MUTEI7.js";
import {
  require_dns_zone_file
} from "./chunk-N6CRKRWT.js";
import {
  require_docker
} from "./chunk-HD5J7JBU.js";
import {
  require_dot
} from "./chunk-MYWTK2HO.js";
import {
  require_ebnf
} from "./chunk-ZH2ZJPLK.js";
import {
  require_editorconfig
} from "./chunk-V3CGCLIU.js";
import {
  require_eiffel
} from "./chunk-CC4AC43T.js";
import {
  require_csv
} from "./chunk-QH6GPBA5.js";
import {
  require_cypher
} from "./chunk-I47QPSFI.js";
import {
  require_d
} from "./chunk-5J2KEJR4.js";
import {
  require_dart
} from "./chunk-CEVYEM37.js";
import {
  require_dataweave
} from "./chunk-TOOKAHMI.js";
import {
  require_dax
} from "./chunk-KYIP4QF6.js";
import {
  require_dhall
} from "./chunk-ISZQN6QE.js";
import {
  require_diff
} from "./chunk-LKK63X7B.js";
import {
  require_concurnas
} from "./chunk-R3BNUFRZ.js";
import {
  require_coq
} from "./chunk-JVZFJTIA.js";
import {
  require_crystal
} from "./chunk-XCPAZRQZ.js";
import {
  require_ruby
} from "./chunk-UPCSM6UC.js";
import {
  require_cshtml
} from "./chunk-X2D3LY6P.js";
import {
  require_csp
} from "./chunk-JCBUHQCC.js";
import {
  require_css_extras
} from "./chunk-VECI7PLO.js";
import {
  require_cfscript
} from "./chunk-IWN52EIT.js";
import {
  require_chaiscript
} from "./chunk-MZ7PIJEL.js";
import {
  require_cil
} from "./chunk-5V3RVKMI.js";
import {
  require_clojure
} from "./chunk-U3ARX3BU.js";
import {
  require_cmake
} from "./chunk-JTASTH7Q.js";
import {
  require_cobol
} from "./chunk-GDL2BCT4.js";
import {
  require_coffeescript
} from "./chunk-ZZIM7O2V.js";
import {
  require_bicep
} from "./chunk-TJQUUHXI.js";
import {
  require_birb
} from "./chunk-XEZFVTHD.js";
import {
  require_bison
} from "./chunk-I7S7IH43.js";
import {
  require_bnf
} from "./chunk-X7INRKML.js";
import {
  require_brainfuck
} from "./chunk-GS4QXI3X.js";
import {
  require_brightscript
} from "./chunk-UHAGGP32.js";
import {
  require_bro
} from "./chunk-5ETYQU57.js";
import {
  require_bsl
} from "./chunk-27ATXTHL.js";
import {
  require_autohotkey
} from "./chunk-SH2IYZCK.js";
import {
  require_autoit
} from "./chunk-NZTJRA4C.js";
import {
  require_avisynth
} from "./chunk-HIRJPTRV.js";
import {
  require_avro_idl
} from "./chunk-QC6VNX6U.js";
import {
  require_bash
} from "./chunk-PV6PWUPF.js";
import {
  require_basic
} from "./chunk-EYTESG7S.js";
import {
  require_batch
} from "./chunk-UBN66CRF.js";
import {
  require_bbcode
} from "./chunk-OZ7F5KGQ.js";
import {
  require_arduino
} from "./chunk-RTMD5EOO.js";
import {
  require_cpp
} from "./chunk-WFC3GL2K.js";
import {
  require_arff
} from "./chunk-7KYS2SZB.js";
import {
  require_asciidoc
} from "./chunk-TH3RSPYC.js";
import {
  require_asm6502
} from "./chunk-KBYZU4WT.js";
import {
  require_asmatmel
} from "./chunk-ZVW3JSL7.js";
import {
  require_aspnet
} from "./chunk-CNJG2UA7.js";
import {
  require_csharp
} from "./chunk-6QEXODKR.js";
import {
  require_antlr4
} from "./chunk-5XKWEXLW.js";
import {
  require_apacheconf
} from "./chunk-IC3LEOKS.js";
import {
  require_apex
} from "./chunk-3IH5SWVE.js";
import {
  require_sql
} from "./chunk-WVBVPCK5.js";
import {
  require_apl
} from "./chunk-RG7R6Q2A.js";
import {
  require_applescript
} from "./chunk-ELGULZBG.js";
import {
  require_aql
} from "./chunk-OHGV5N7R.js";
import {
  require_c
} from "./chunk-L5D4RGJU.js";
import {
  require_abap
} from "./chunk-7Q7GCXYH.js";
import {
  require_abnf
} from "./chunk-JBLTX3X5.js";
import {
  require_actionscript
} from "./chunk-AQZA3QW6.js";
import {
  require_ada
} from "./chunk-YVT57GJX.js";
import {
  require_agda
} from "./chunk-RXQE6BFS.js";
import {
  require_al
} from "./chunk-DAVF25V4.js";
import {
  __commonJS
} from "./chunk-5WRI5ZAA.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-OJ3J73FG.js.map
