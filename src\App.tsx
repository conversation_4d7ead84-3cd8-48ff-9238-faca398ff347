import { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import InputForm from './components/InputForm';
import TreeVisualizer from './components/TreeVisualizer';
import Controls from './components/Controls';
import ExplanationPanel from './components/ExplanationPanel';

type TreeType = 'avl' | 'rb' | 'btree';

const App = () => {
  const [darkMode, setDarkMode] = useState(false);
  const [values, setValues] = useState<number[]>([]);
  const [treeType, setTreeType] = useState<TreeType>('avl');
  const [isPlaying, setIsPlaying] = useState(false);
  const [animationSpeed, setAnimationSpeed] = useState(1);
  const [currentStep, setCurrentStep] = useState(0);
  const [totalSteps, setTotalSteps] = useState(0);

  useEffect(() => {
    // Check for user preference
    const isDarkMode = localStorage.getItem('darkMode') === 'true';
    setDarkMode(isDarkMode);

    // Apply dark mode class to body
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, []);

  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('darkMode', String(newDarkMode));

    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const handleFormSubmit = (newValues: number[], newTreeType: TreeType) => {
    setValues(newValues);
    setTreeType(newTreeType);
    setCurrentStep(0);
    setIsPlaying(false);
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleReset = () => {
    setCurrentStep(0);
    setIsPlaying(false);
  };

  const handleSpeedChange = (speed: number) => {
    setAnimationSpeed(speed);
  };

  const handleStepChange = (step: number, total: number) => {
    setCurrentStep(step);
    setTotalSteps(total);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <Layout darkMode={darkMode} toggleDarkMode={toggleDarkMode}>
        <Routes>
          <Route path="/" element={
            <div className="p-4">
              <h1 className="text-3xl font-bold mb-6">Interactive Tree Visualization</h1>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <InputForm onSubmit={handleFormSubmit} />

                  {values.length > 0 && (
                    <>
                      <Controls
                        isPlaying={isPlaying}
                        onPlayPause={handlePlayPause}
                        onPrevStep={handlePrevStep}
                        onNextStep={handleNextStep}
                        onReset={handleReset}
                        onSpeedChange={handleSpeedChange}
                        currentStep={currentStep}
                        totalSteps={totalSteps}
                        animationSpeed={animationSpeed}
                      />

                      <TreeVisualizer
                        treeType={treeType}
                        values={values}
                        animationSpeed={animationSpeed}
                        isPlaying={isPlaying}
                        onStepChange={handleStepChange}
                      />
                    </>
                  )}
                </div>

                <div>
                  <ExplanationPanel treeType={treeType} />
                </div>
              </div>
            </div>
          } />
        </Routes>
      </Layout>
    </div>
  );
};

export default App;
