@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-gray-900 dark:text-gray-100;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600;
  }

  .input {
    @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white;
  }

  .tree-node {
    @apply flex items-center justify-center rounded-full transition-all duration-300;
  }

  .tree-node-avl {
    @apply bg-green-500 text-white;
  }

  .tree-node-rb-red {
    @apply bg-red-500 text-white;
  }

  .tree-node-rb-black {
    @apply bg-gray-900 text-white dark:bg-black;
  }

  .tree-node-btree {
    @apply bg-blue-500 text-white;
  }

  /* Fix for dark mode text visibility */
  .dark text {
    @apply fill-white;
  }

  /* Ensure height labels are visible in dark mode */
  .dark svg text[dy="-1.2em"] {
    @apply fill-gray-300;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    @apply h-2 w-2;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
  }
}
