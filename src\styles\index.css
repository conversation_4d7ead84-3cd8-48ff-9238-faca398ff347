@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-gray-900 dark:text-gray-100;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }

  .btn-primary {
    background-color: #ECC232; /* Gold from the new palette */
    color: #333;
  }

  .btn-primary:hover {
    background-color: #d9b12e; /* Slightly darker gold */
  }

  .dark .btn-primary {
    background-color: #ECC232;
    color: #333;
  }

  .dark .btn-primary:hover {
    background-color: #d9b12e;
  }

  .btn-secondary {
    background-color: #FFE900; /* Yellow from the new palette */
    color: #333;
  }

  .btn-secondary:hover {
    background-color: #e6d200; /* Slightly darker yellow */
  }

  .dark .btn-secondary {
    background-color: #FFE900;
    color: #333;
  }

  .dark .btn-secondary:hover {
    background-color: #e6d200;
  }

  .input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #ECC232; /* Gold border */
    border-radius: 0.375rem;
    outline: none;
  }

  .input:focus {
    box-shadow: 0 0 0 2px rgba(236, 194, 50, 0.5); /* Gold ring */
    border-color: #d9b12e; /* Darker gold border when focused */
  }

  .dark .input {
    background-color: #1a1a1a;
    border-color: #ECC232; /* Gold border in dark mode */
    color: white;
  }

  .dark .input:focus {
    box-shadow: 0 0 0 2px rgba(236, 194, 50, 0.5); /* Gold ring in dark mode */
  }

  .tree-node {
    @apply flex items-center justify-center rounded-full transition-all duration-300;
  }

  /* Custom color palette */
  .color-gold {
    background-color: #ECC232;
    color: #333; /* Dark text for contrast */
  }

  .color-yellow {
    background-color: #FFE900;
    color: #333; /* Dark text for contrast */
  }

  .color-gray {
    background-color: #BDBCB8;
    color: #333; /* Dark text for contrast */
  }

  .tree-node-avl {
    background-color: #FFE900; /* Bright yellow */
    color: #333;
  }

  .tree-node-rb-red {
    background-color: #ECC232; /* Gold */
    color: #333;
  }

  .tree-node-rb-black {
    background-color: #BDBCB8; /* Gray */
    color: #333;
  }

  .tree-node-btree {
    background-color: #FFE900; /* Bright yellow */
    color: #333;
  }

  /* Fix for dark mode text visibility */
  .dark text {
    @apply fill-white;
  }

  /* Ensure height labels are visible in dark mode */
  .dark svg text[dy="-1.2em"] {
    @apply fill-gray-300;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    @apply h-2 w-2;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
  }

  /* Tree visualization styles */
  .tree-container {
    @apply overflow-auto border border-gray-300 rounded-lg bg-white dark:bg-gray-800 min-h-[500px] max-h-[600px] scrollbar-thin;
  }

  .tree-container.dragging {
    @apply cursor-grabbing;
  }

  .tree-container:not(.dragging) {
    @apply cursor-grab;
  }

  .tree-content {
    @apply min-w-[1200px] min-h-[800px] transform-gpu;
    transform-origin: center center;
    transition: transform 0.2s ease-out;
  }

  .tree-content.dragging {
    transition: none;
  }
}
