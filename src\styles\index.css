@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-gray-900 dark:text-gray-100;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }

  .btn-primary {
    background-color: #F2BFA4; /* Peach from the new palette */
    color: #333;
  }

  .btn-primary:hover {
    background-color: #eaad8e; /* Slightly darker peach */
  }

  .dark .btn-primary {
    background-color: #F2BFA4;
    color: #333;
  }

  .dark .btn-primary:hover {
    background-color: #eaad8e;
  }

  .btn-secondary {
    background-color: #F5E7DE; /* Cream from the new palette */
    color: #333;
  }

  .btn-secondary:hover {
    background-color: #ecdacb; /* Slightly darker cream */
  }

  .dark .btn-secondary {
    background-color: #F5E7DE;
    color: #333;
  }

  .dark .btn-secondary:hover {
    background-color: #ecdacb;
  }

  .input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #F2BFA4; /* Peach border */
    border-radius: 0.375rem;
    outline: none;
  }

  .input:focus {
    box-shadow: 0 0 0 2px rgba(242, 191, 164, 0.5); /* Peach ring */
    border-color: #eaad8e; /* Darker peach border when focused */
  }

  .dark .input {
    background-color: #1a1a1a;
    border-color: #F2BFA4; /* Peach border in dark mode */
    color: white;
  }

  .dark .input:focus {
    box-shadow: 0 0 0 2px rgba(242, 191, 164, 0.5); /* Peach ring in dark mode */
  }

  .tree-node {
    @apply flex items-center justify-center rounded-full transition-all duration-300;
  }

  /* Custom color palette */
  .color-cream {
    background-color: #F5E7DE;
    color: #333; /* Dark text for contrast */
  }

  .color-peach {
    background-color: #F2BFA4;
    color: #333; /* Dark text for contrast */
  }

  .tree-node-avl {
    background-color: #F5E7DE;
    color: #333;
  }

  .tree-node-rb-red {
    background-color: #F2BFA4;
    color: #333;
  }

  .tree-node-rb-black {
    background-color: #F5E7DE;
    color: #333;
  }

  .tree-node-btree {
    background-color: #F2BFA4;
    color: #333;
  }

  /* Fix for dark mode text visibility */
  .dark text {
    @apply fill-white;
  }

  /* Ensure height labels are visible in dark mode */
  .dark svg text[dy="-1.2em"] {
    @apply fill-gray-300;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    @apply h-2 w-2;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
  }

  /* Tree visualization styles */
  .tree-container {
    @apply overflow-auto border border-gray-300 rounded-lg bg-white dark:bg-gray-800 min-h-[500px] max-h-[600px] scrollbar-thin;
  }

  .tree-container.dragging {
    @apply cursor-grabbing;
  }

  .tree-container:not(.dragging) {
    @apply cursor-grab;
  }

  .tree-content {
    @apply min-w-[1200px] min-h-[800px] transform-gpu;
    transform-origin: center center;
    transition: transform 0.2s ease-out;
  }

  .tree-content.dragging {
    transition: none;
  }
}
