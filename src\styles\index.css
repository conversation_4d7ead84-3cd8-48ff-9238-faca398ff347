@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-gray-900 dark:text-gray-100;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }

  .btn-primary {
    background-color: #F15B42; /* Orange from the new palette */
    color: white;
  }

  .btn-primary:hover {
    background-color: #e04e36; /* Slightly darker orange */
  }

  .dark .btn-primary {
    background-color: #F15B42;
  }

  .dark .btn-primary:hover {
    background-color: #e04e36;
  }

  .btn-secondary {
    background-color: #7CAADC; /* Light blue from the new palette */
    color: #2C3D73; /* Dark blue text */
  }

  .btn-secondary:hover {
    background-color: #6b99cb; /* Slightly darker light blue */
  }

  .dark .btn-secondary {
    background-color: #2C3D73; /* Dark blue in dark mode */
    color: white;
  }

  .dark .btn-secondary:hover {
    background-color: #3a4b81; /* Slightly lighter dark blue */
  }

  .input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #7CAADC; /* Light blue border */
    border-radius: 0.375rem;
    outline: none;
  }

  .input:focus {
    box-shadow: 0 0 0 2px rgba(124, 170, 220, 0.5); /* Light blue ring */
    border-color: #2C3D73; /* Dark blue border when focused */
  }

  .dark .input {
    background-color: #1a1a1a;
    border-color: #2C3D73; /* Dark blue border in dark mode */
    color: white;
  }

  .dark .input:focus {
    box-shadow: 0 0 0 2px rgba(44, 61, 115, 0.5); /* Dark blue ring in dark mode */
  }

  .tree-node {
    @apply flex items-center justify-center rounded-full transition-all duration-300;
  }

  /* Custom color palette */
  .color-yellow {
    background-color: #FFD372;
    color: #2C3D73; /* Dark blue text for contrast */
  }

  .color-orange {
    background-color: #F15B42;
    color: white;
  }

  .color-pink {
    background-color: #F49CC4;
    color: #2C3D73; /* Dark blue text for contrast */
  }

  .color-dark-blue {
    background-color: #2C3D73;
    color: white;
  }

  .color-light-blue {
    background-color: #7CAADC;
    color: #2C3D73; /* Dark blue text for contrast */
  }

  .tree-node-avl {
    @apply color-yellow;
  }

  .tree-node-rb-red {
    @apply color-orange;
  }

  .tree-node-rb-black {
    @apply color-dark-blue;
  }

  .tree-node-btree {
    @apply color-light-blue;
  }

  /* Fix for dark mode text visibility */
  .dark text {
    @apply fill-white;
  }

  /* Ensure height labels are visible in dark mode */
  .dark svg text[dy="-1.2em"] {
    @apply fill-gray-300;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    @apply h-2 w-2;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
  }

  /* Tree visualization styles */
  .tree-container {
    @apply overflow-auto border border-gray-300 rounded-lg bg-white dark:bg-gray-800 min-h-[500px] max-h-[600px] scrollbar-thin;
  }

  .tree-container.dragging {
    @apply cursor-grabbing;
  }

  .tree-container:not(.dragging) {
    @apply cursor-grab;
  }

  .tree-content {
    @apply min-w-[1200px] min-h-[800px] transform-gpu;
    transform-origin: center center;
    transition: transform 0.2s ease-out;
  }

  .tree-content.dragging {
    transition: none;
  }
}
