@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-gray-900 dark:text-gray-100;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }

  .btn-primary {
    background-color: #F02F34; /* Red from the new palette */
    color: #FFFFFF;
  }

  .btn-primary:hover {
    background-color: #d92a2f; /* Slightly darker red */
  }

  .dark .btn-primary {
    background-color: #6552D0; /* Purple for dark mode */
    color: #FFFFFF;
  }

  .dark .btn-primary:hover {
    background-color: #5a47c7; /* Slightly darker purple */
  }

  .btn-secondary {
    background-color: #E7D3BB; /* Beige from the new palette */
    color: #000000;
  }

  .btn-secondary:hover {
    background-color: #d9c5ad; /* Slightly darker beige */
  }

  .dark .btn-secondary {
    background-color: #17203D; /* Dark blue for dark mode */
    color: #FFFFFF;
  }

  .dark .btn-secondary:hover {
    background-color: #1e2a4a; /* Slightly lighter dark blue */
  }

  .input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #F02F34; /* Red border */
    border-radius: 0.375rem;
    outline: none;
  }

  .input:focus {
    box-shadow: 0 0 0 2px rgba(240, 47, 52, 0.5); /* Red ring */
    border-color: #d92a2f; /* Darker red border when focused */
  }

  .dark .input {
    background-color: #17203D; /* Dark blue background */
    border-color: #6552D0; /* Purple border in dark mode */
    color: #FFFFFF;
  }

  .dark .input:focus {
    box-shadow: 0 0 0 2px rgba(101, 82, 208, 0.5); /* Purple ring in dark mode */
    border-color: #5a47c7; /* Darker purple border when focused */
  }

  /* Logo styling */
  .logo-text {
    color: #F02F34;
    font-weight: bold;
  }

  .dark .logo-text {
    color: #6552D0; /* Purple in dark mode */
  }

  .logo-large {
    font-size: 2.5rem;
  }

  .logo-header {
    font-size: 2.5rem;
    font-weight: bold;
    color: #F02F34;
  }

  .dark .logo-header {
    color: #6552D0; /* Purple in dark mode */
  }

  .tree-node {
    @apply flex items-center justify-center rounded-full transition-all duration-300;
  }

  /* Custom color palette */
  .color-red {
    background-color: #F02F34;
    color: #FFFFFF; /* White text for contrast */
  }

  .color-beige {
    background-color: #E7D3BB;
    color: #000000; /* Black text for contrast */
  }

  .color-white {
    background-color: #FFFFFF;
    color: #000000; /* Black text for contrast */
  }

  .color-black {
    background-color: #000000;
    color: #FFFFFF; /* White text for contrast */
  }

  .tree-node-avl {
    background-color: #F02F34; /* Red */
    color: #FFFFFF;
  }

  .dark .tree-node-avl {
    background-color: #6552D0; /* Purple in dark mode */
    color: #FFFFFF;
  }

  .tree-node-rb-red {
    background-color: #F02F34; /* Red */
    color: #FFFFFF;
  }

  .dark .tree-node-rb-red {
    background-color: #6552D0; /* Purple in dark mode */
    color: #FFFFFF;
  }

  .tree-node-rb-black {
    background-color: #000000; /* Black */
    color: #FFFFFF;
  }

  .dark .tree-node-rb-black {
    background-color: #17203D; /* Dark blue in dark mode */
    color: #FFFFFF;
  }

  .tree-node-btree {
    background-color: #E7D3BB; /* Beige */
    color: #000000;
  }

  .dark .tree-node-btree {
    background-color: #17203D; /* Dark blue in dark mode */
    color: #FFFFFF;
  }

  /* Fix for dark mode text visibility */
  .dark text {
    @apply fill-white;
  }

  /* Ensure height labels are visible in dark mode */
  .dark svg text[dy="-1.2em"] {
    @apply fill-gray-300;
  }

  /* Custom scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    @apply h-2 w-2;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
  }

  /* Tree visualization styles */
  .tree-container {
    @apply overflow-auto border border-gray-300 rounded-lg bg-white dark:bg-gray-800 min-h-[500px] max-h-[600px] scrollbar-thin;
  }

  .tree-container.dragging {
    @apply cursor-grabbing;
  }

  .tree-container:not(.dragging) {
    @apply cursor-grab;
  }

  .tree-content {
    @apply min-w-[1200px] min-h-[800px] transform-gpu;
    transform-origin: center center;
    transition: transform 0.2s ease-out;
  }

  .tree-content.dragging {
    transition: none;
  }
}
