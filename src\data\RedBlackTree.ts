export enum Color {
  RED = 'red',
  BLACK = 'black'
}

export interface RBNode {
  value: number;
  color: Color;
  left: RBNode | null;
  right: RBNode | null;
  parent: RBNode | null;
  x?: number;
  y?: number;
  id: string;
}

export interface RBOperation {
  type: 'insert' | 'rotate-left' | 'rotate-right' | 'color-flip' | 'recolor';
  node?: RBNode;
  description: string;
}

export class RedBlackTree {
  root: RBNode | null = null;
  NIL: RBNode;
  operations: RBOperation[] = [];

  constructor() {
    // Create sentinel NIL node
    this.NIL = {
      value: -1,
      color: Color.BLACK,
      left: null,
      right: null,
      parent: null,
      id: 'nil'
    };
    this.root = this.NIL;
  }

  // Left rotation
  private rotateLeft(x: RBNode): void {
    const y = x.right as RBNode;
    x.right = y.left;

    if (y.left !== this.NIL) {
      y.left.parent = x;
    }

    y.parent = x.parent;

    if (x.parent === null) {
      this.root = y;
    } else if (x === x.parent.left) {
      x.parent.left = y;
    } else {
      x.parent.right = y;
    }

    y.left = x;
    x.parent = y;

    this.operations.push({
      type: 'rotate-left',
      node: x,
      description: `Left rotation performed at node ${x.value}`
    });
  }

  // Right rotation
  private rotateRight(y: RBNode): void {
    const x = y.left as RBNode;
    y.left = x.right;

    if (x.right !== this.NIL) {
      x.right.parent = y;
    }

    x.parent = y.parent;

    if (y.parent === null) {
      this.root = x;
    } else if (y === y.parent.left) {
      y.parent.left = x;
    } else {
      y.parent.right = x;
    }

    x.right = y;
    y.parent = x;

    this.operations.push({
      type: 'rotate-right',
      node: y,
      description: `Right rotation performed at node ${y.value}`
    });
  }

  // Insert a node
  insert(value: number): void {
    // Add a separator operation to indicate a new value insertion
    if (this.operations.length > 0) {
      this.operations.push({
        type: 'insert',
        description: `Starting insertion of value ${value}`
      });
    }

    // Create new node
    const newNode: RBNode = {
      value,
      color: Color.RED, // New nodes are always red
      left: this.NIL,
      right: this.NIL,
      parent: null,
      id: `node-${value}-${Date.now()}`
    };

    this.operations.push({
      type: 'insert',
      node: newNode,
      description: `Created new RED node with value ${value}`
    });

    // Find position to insert
    let y: RBNode | null = null;
    let x: RBNode | null = this.root;

    while (x !== this.NIL && x !== null) {
      y = x;
      if (newNode.value < x.value) {
        x = x.left;
      } else {
        x = x.right;
      }
    }

    // Set parent of new node
    newNode.parent = y;

    // Insert node in the tree
    if (y === null) {
      this.root = newNode; // Tree was empty
    } else if (newNode.value < y.value) {
      y.left = newNode;
    } else {
      y.right = newNode;
    }

    this.operations.push({
      type: 'insert',
      node: newNode,
      description: `Inserted node ${newNode.value} into the tree`
    });

    // If the new node is the root, color it black and return
    if (newNode.parent === null) {
      newNode.color = Color.BLACK;
      this.operations.push({
        type: 'recolor',
        node: newNode,
        description: `Recolored root node ${newNode.value} to BLACK`
      });
      return;
    }

    // If the grandparent is null, return
    if (newNode.parent.parent === null) {
      return;
    }

    // Fix the tree to maintain Red-Black properties
    this.fixInsert(newNode);
  }

  // Fix the tree after insertion
  private fixInsert(k: RBNode): void {
    let u: RBNode;

    while (k.parent !== null && k.parent.color === Color.RED) {
      if (k.parent === k.parent.parent?.right) {
        u = k.parent.parent.left as RBNode; // Uncle

        // Case 1: Uncle is red
        if (u.color === Color.RED) {
          u.color = Color.BLACK;
          k.parent.color = Color.BLACK;
          k.parent.parent.color = Color.RED;

          this.operations.push({
            type: 'color-flip',
            node: k.parent.parent,
            description: `Color flip: Uncle and parent to BLACK, grandparent to RED`
          });

          k = k.parent.parent;
        } else {
          // Case 2: Uncle is black, k is left child
          if (k === k.parent.left) {
            k = k.parent;
            this.rotateRight(k);
          }

          // Case 3: Uncle is black, k is right child
          if (k.parent) {
            k.parent.color = Color.BLACK;
            if (k.parent.parent) {
              k.parent.parent.color = Color.RED;

              this.operations.push({
                type: 'recolor',
                node: k.parent,
                description: `Recolored parent to BLACK and grandparent to RED`
              });

              this.rotateLeft(k.parent.parent);
            }
          }
        }
      } else {
        u = k.parent.parent?.right as RBNode; // Uncle

        // Case 1: Uncle is red
        if (u && u.color === Color.RED) {
          u.color = Color.BLACK;
          k.parent.color = Color.BLACK;
          k.parent.parent.color = Color.RED;

          this.operations.push({
            type: 'color-flip',
            node: k.parent.parent,
            description: `Color flip: Uncle and parent to BLACK, grandparent to RED`
          });

          k = k.parent.parent;
        } else {
          // Case 2: Uncle is black, k is right child
          if (k === k.parent.right) {
            k = k.parent;
            this.rotateLeft(k);
          }

          // Case 3: Uncle is black, k is left child
          if (k.parent) {
            k.parent.color = Color.BLACK;
            if (k.parent.parent) {
              k.parent.parent.color = Color.RED;

              this.operations.push({
                type: 'recolor',
                node: k.parent,
                description: `Recolored parent to BLACK and grandparent to RED`
              });

              this.rotateRight(k.parent.parent);
            }
          }
        }
      }

      if (k === this.root) {
        break;
      }
    }

    if (this.root) {
      this.root.color = Color.BLACK;
    }
  }

  // Calculate the coordinates for visualization
  calculateCoordinates(
    node: RBNode | null = this.root,
    depth: number = 0,
    position: number = 0,
    horizontalSpacing: number = 60,
    verticalSpacing: number = 60
  ): void {
    if (!node || node === this.NIL) return;

    // Calculate x and y coordinates
    node.y = depth * verticalSpacing + 40;
    node.x = position * horizontalSpacing + 120; // Shift right by adding a larger offset

    // Get the total number of nodes to adjust spacing
    const totalNodes = this.getAllNodes().length;
    const maxDepth = this.getMaxDepth(this.root);

    // Calculate a scaling factor based on tree size
    const scaleFactor = Math.max(1, Math.min(3, Math.log2(totalNodes + 1)));

    // Calculate coordinates for left and right children with adaptive spacing
    if (node.left && node.left !== this.NIL) {
      this.calculateCoordinates(
        node.left,
        depth + 1,
        position - Math.pow(2, Math.max(0, 2 - depth)) / scaleFactor,
        horizontalSpacing,
        verticalSpacing
      );
    }

    if (node.right && node.right !== this.NIL) {
      this.calculateCoordinates(
        node.right,
        depth + 1,
        position + Math.pow(2, Math.max(0, 2 - depth)) / scaleFactor,
        horizontalSpacing,
        verticalSpacing
      );
    }
  }

  // Helper method to get the maximum depth of the tree
  private getMaxDepth(node: RBNode | null): number {
    if (!node || node === this.NIL) return 0;
    return Math.max(this.getMaxDepth(node.left), this.getMaxDepth(node.right)) + 1;
  }

  // Get all nodes for visualization
  getAllNodes(): RBNode[] {
    const nodes: RBNode[] = [];

    const traverse = (node: RBNode | null) => {
      if (node && node !== this.NIL) {
        nodes.push(node);
        traverse(node.left);
        traverse(node.right);
      }
    };

    traverse(this.root);
    return nodes;
  }

  // Get all edges for visualization
  getEdges(): { source: RBNode; target: RBNode }[] {
    const edges: { source: RBNode; target: RBNode }[] = [];

    const traverse = (node: RBNode | null) => {
      if (node && node !== this.NIL) {
        if (node.left && node.left !== this.NIL) {
          edges.push({ source: node, target: node.left });
          traverse(node.left);
        }
        if (node.right && node.right !== this.NIL) {
          edges.push({ source: node, target: node.right });
          traverse(node.right);
        }
      }
    };

    traverse(this.root);
    return edges;
  }
}
