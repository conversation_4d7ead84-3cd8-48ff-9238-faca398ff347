{"name": "treezy", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}, "dependencies": {"@types/d3": "^7.4.3", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "d3": "^7.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0"}}